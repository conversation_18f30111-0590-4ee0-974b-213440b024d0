package com.hdec.common.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AlarmDdcRuleCommon implements Serializable {

    /**
     * 是否存在【仅用于判断缓存是不存在还是存在但空值】
     */
    private boolean exist = true;

    /**
     * 测点ID
     */
    private Integer pointId;

    /**
     * 仪器ID
     */
    private Integer instId;

    /**
     * 测点编号
     */
    private String pointNo;

    /**
     * 超限分量
     */
    private Integer overAttrId;

    /**
     * 超限阈值
     */
    private Double overThreshold;

    /**
     * 超限时间（秒）
     */
    private Integer overTime;

    /**
     * 回溯分量
     */
    private Integer retraceAttrId;

    /**
     * 回溯阈值
     */
    private Double retraceThreshold;

    /**
     * 回溯时间（分钟）
     */
    private Integer retraceTime;

    /**
     * 回溯超限百分比
     */
    private Integer ratio;

    /**
     * 风场编码
     */
    private String fieldNum;

    public AlarmDdcRuleCommon(boolean exist) {
        this.exist = exist;
    }
}
