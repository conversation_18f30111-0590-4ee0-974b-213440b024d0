package com.hdec.data.cache;

import com.alibaba.fastjson.JSON;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.hdec.common.constant.Constant;
import com.hdec.common.constant.RedisKey;
import com.hdec.common.domain.*;
import com.hdec.common.util.CommonUtil;
import com.hdec.common.util.FormulaDependencyUtils;
import com.hdec.data.feign.MonitorService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.ParametersAreNonnullByDefault;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * local cache manager
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Component
public class LocalCacheManager implements CommandLineRunner {
    private static final Logger logger = LoggerFactory.getLogger(LocalCacheManager.class);
    private static final int CACHE_REFRESH_TIME = 1;

    private static final int LONG_CACHE_REFRESH_TIME = 10;

    private static final int SINGLE_CACHE_KEY = 1;

    @Value("${deploy.fieldNum}")
    private String fieldNum;

    @Value("${data.access.log.enable:false}")
    private boolean logEnable;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private MonitorService monitorService;

    @Override
    public void run(String... args) throws Exception {
        /*  初始化加载一次，避免冷启动  */
        getMountCache("dynamic", "1001", 1);
        getAttrCache(0);
        getPointParamCache("0", "0", "0");
        getFormulaCache();
        getPointCache(0);
        getAlarmRuleCache("0-0");
    }

    private static final Cache<String, Boolean> EMPTY_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(CACHE_REFRESH_TIME, TimeUnit.MINUTES)
            .build();

    /**
     * 存在的测点KEY
     */
    private List<Integer> existPointKeys;

    /**
     * 测点缓存
     */
    private final LoadingCache<Integer, PointCommon> POINT_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(CACHE_REFRESH_TIME, TimeUnit.MINUTES)
            .build(new CacheLoader<Integer, PointCommon>() {
                @ParametersAreNonnullByDefault
                @Override
                public PointCommon load(Integer key) throws Exception {
                    /*  已经初始化，且不包含当前Key  */
                    if (existPointKeys != null && !existPointKeys.contains(key)) {
                        return null;
                    } else {
                        /*  初始化  */
                        if (logEnable) {
                            logger.info("++++++++++测点缓存加载++++++++++");
                        }
                        logger.info("**********测点缓存加载**********");
                        List<PointCommon> points = monitorService.getPointsByFieldNum(fieldNum);
                        if (points == null) {
                            logger.error("测点缓存加载异常-查询为空");
                            return null;
                        }
                        /*  初始化存在的KEY  */
                        existPointKeys = points.stream().map(PointCommon::getId)
                                .filter(Objects::nonNull).distinct().collect(Collectors.toList());
                        /*  写入所有缓存  */
                        for (PointCommon point : points) {
                            if (point != null && point.getId() != null) {
                                POINT_CACHE.put(point.getId(), point);
                            }
                        }
                        if (logEnable) {
                            logger.info("==========测点缓存加载==========");
                        }
                        return points.stream().filter(point -> point.getId().equals(key)).findFirst().orElse(null);
                    }
                }
            });

    /**
     * 获取测点信息
     *
     * @param pointId 测点ID
     * @return {@link PointCommon }
     */
    public PointCommon getPointCache(Integer pointId) {
        try {
            return POINT_CACHE.get(pointId);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 存在的分量信息KEY
     */
    private List<Integer> existAttrKeys;

    /**
     * 分量信息缓存
     */
    private final LoadingCache<Integer, AttrCommon> ATTR_INFO_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(CACHE_REFRESH_TIME, TimeUnit.MINUTES)
            .build(new CacheLoader<Integer, AttrCommon>() {
                @ParametersAreNonnullByDefault
                @Override
                public AttrCommon load(Integer key) throws Exception {
                    /*  已经初始化，且不包含当前Key  */
                    if (existAttrKeys != null && !existAttrKeys.contains(key)) {
                        return null;
                    } else {
                        /*  初始化  */
                        if (logEnable) {
                            logger.info("**********分量信息缓存加载**********");
                        }
                        List<AttrCommon> attrs = monitorService.allAttrWithInstByField(fieldNum);
                        if (attrs == null) {
                            logger.error("分量信息缓存加载异常-查询为空");
                            return null;
                        }
                        /*  初始化存在的KEY  */
                        existAttrKeys = attrs.stream().map(AttrCommon::getId)
                                .filter(Objects::nonNull).distinct().collect(Collectors.toList());
                        /*  写入所有缓存  */
                        for (AttrCommon attr : attrs) {
                            if (attr != null && attr.getId() != null) {
                                ATTR_INFO_CACHE.put(attr.getId(), attr);
                            }
                        }
                        if (logEnable) {
                            logger.info("==========分量信息缓存加载==========");
                        }
                        return attrs.stream().filter(attr -> attr.getId().equals(key)).findFirst().orElse(null);
                    }
                }
            });

    /**
     * 获取缓存分量信息
     *
     * @param attrId attr id
     * @return {@link AttrCommon }
     */
    public AttrCommon getAttrCache(Integer attrId) {
        try {
            return ATTR_INFO_CACHE.get(attrId);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 存在的测点参数KEY
     */
    private Map<String, Boolean> existParamKeys;

    /**
     * 测点参数缓存
     */
    private final LoadingCache<String, Double> PARAM_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(CACHE_REFRESH_TIME, TimeUnit.MINUTES)
            .build(new CacheLoader<String, Double>() {
                @ParametersAreNonnullByDefault
                @Override
                public Double load(String key) throws Exception {
                    /*  已经初始化，且不包含当前Key  */
                    if (existParamKeys != null && existParamKeys.get(key) == null) {
                        return null;
                    } else {
                        /*  加载缓存  */
                        if (logEnable) {
                            logger.info("**********测点参数缓存加载**********");
                        }
                        List<PointParamCommon> params = monitorService.allParamsByField(fieldNum);
                        if (params == null) {
                            logger.error("测点参数缓存加载异常-查询为空");
                            return null;
                        }
                        Map<String, Double> paramMap = params.stream().filter(e -> e.getParamValue() != null &&
                                !e.getParamValue().trim().isEmpty()).collect(
                                Collectors.toMap(e -> e.getMeasurePointId() + "_" + e.getParamId() + "_" + e.getDirection(),
                                        v -> Double.valueOf(v.getParamValue())));
                        /*  初始化存在的KEY  */
                        existParamKeys = paramMap.keySet().stream().collect(Collectors.toMap(k -> k, v -> true));
                        /*  写入所有缓存  */
                        PARAM_CACHE.putAll(paramMap);
                        if (logEnable) {
                            logger.info("==========测点参数缓存加载==========");
                        }
                        return paramMap.get(key);
                    }
                }
            });

    /**
     * 获取缓存测点参数
     *
     * @return {@link Double }
     */
    public Double getPointParamCache(String point, String paramId, String direct) {
        String key = point + "_" + paramId + "_" + direct;
        try {
            return PARAM_CACHE.get(key);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 公式缓存
     */
    private final LoadingCache<Integer, List<FormulaCommon>> FORMULA_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(LONG_CACHE_REFRESH_TIME, TimeUnit.HOURS)
            .build(new CacheLoader<Integer, List<FormulaCommon>>() {
                @ParametersAreNonnullByDefault
                @Override
                public List<FormulaCommon> load(Integer key) throws Exception {
                    /*  加载缓存  */
                    if (logEnable) {
                        logger.info("**********公式缓存加载**********");
                    }
                    List<FormulaCommon> formulas = monitorService.getAllPointFormulas();
                    if (formulas == null) {
                        logger.error("公式缓存加载异常-查询为空");
                        return Collections.emptyList();
                    }
                    if (logEnable) {
                        logger.info("==========公式缓存加载==========");
                    }
                    FORMULA_UP_DEPS_CACHE.invalidateAll();
                    return formulas;
                }
            });

    /**
     * 获取缓存公式
     *
     * @return {@link List}<{@link FormulaCommon }>
     */
    public List<FormulaCommon> getFormulaCache() {
        try {
            return FORMULA_CACHE.get(SINGLE_CACHE_KEY);
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }

    /**
     * 公式向上分析缓存
     */
    private final LoadingCache<String, List<FormulaCommon>> FORMULA_UP_DEPS_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(CACHE_REFRESH_TIME, TimeUnit.HOURS)
            .build(new CacheLoader<String, List<FormulaCommon>>() {
                @ParametersAreNonnullByDefault
                @Override
                public List<FormulaCommon> load(String key) throws Exception {
                    List<FormulaCommon> formulas = getFormulaCache();
                    if (formulas == null) {
                        return Collections.emptyList();
                    }
                    return FormulaDependencyUtils.resolveReverseDependencyChain(key, formulas);
                }
            });

    /**
     * 获取缓存公式向上分析
     *
     * @param point  point
     * @param attr   attr
     * @param direct direct
     * @return {@link List }<{@link FormulaCommon }>
     */
    public List<FormulaCommon> getUpDepsFormulaCache(Integer point, Integer attr, Integer direct) {
        String key = point + ":" + attr + ":" + direct;
        try {
            return FORMULA_UP_DEPS_CACHE.get(key);
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }

    /**
     * 测点挂载信息缓存
     */

    /**
     * 存在的分量信息KEY
     */
    private final Map<String, Set<String>> existMountKeys = new HashMap<>();

    private final LoadingCache<String, CollectInstMountCommon> POINT_MOUNT_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(CACHE_REFRESH_TIME, TimeUnit.MINUTES)
            .build(new CacheLoader<String, CollectInstMountCommon>() {
                @ParametersAreNonnullByDefault
                @Override
                public CollectInstMountCommon load(String key) throws Exception {
                    String type = key.split(Constant.SPLIT_STR)[0];
                    /*  已经初始化，且不包含当前Key  */
                    if (existMountKeys.get(type) != null && !existMountKeys.get(type).contains(key)) {
                        return null;
                    } else {
                        /* 从 Redis 加载 */
                        String redisKey = RedisKey.AUTO_MOUNT + type + "-" + fieldNum;
                        if (logEnable) {
                            logger.info("**********加载Redis挂载信息：[{}]**********", redisKey);
                        }
                        Object redisCache = redisTemplate.opsForValue().get(redisKey);
                        if (redisCache != null) {
                            List<CollectInstMountCommon> instChannelMounts = JSON.parseArray((String) redisCache, CollectInstMountCommon.class);
                            Map<String, CollectInstMountCommon> channelMountsMap = instChannelMounts.stream()
                                    .collect(Collectors.toMap(e -> CommonUtil.join(Constant.SPLIT_STR, type,
                                            e.getCollectInstNo().replace("AUTO_", ""),
                                            e.getChannel()), e -> e));
                            POINT_MOUNT_CACHE.putAll(channelMountsMap);
                            Set<String> keys = channelMountsMap.keySet();
                            existMountKeys.put(type, keys);
                            return channelMountsMap.get(key);
                        } else {
                            logger.error("**********Redis缓存中未查询到挂载信息**********");
                            return null;
                        }
                    }
                }
            });


    /**
     * 获取缓存测点挂载信息
     *
     * @return {@link CollectInstMountCommon }
     */
    public CollectInstMountCommon getMountCache(String type, String collectInstNo, Integer channel) {
        String key = CommonUtil.join(Constant.SPLIT_STR, type, collectInstNo, channel);
        if (key == null) {
            return null;
        }
        try {
            return POINT_MOUNT_CACHE.get(key);
        } catch (Exception e) {
            return null;
        }
    }


    /**
     * 告警规则空键
     */
    private static final String ALARM_RULE_EMPTY_KEY = "ALARM_RULE";
    private Set<String> existAlarmRuleKeys;
    /**
     * 告警规则缓存
     */
    private final LoadingCache<String, AlarmRuleVo> ALARM_RULE = CacheBuilder.newBuilder()
            .expireAfterWrite(CACHE_REFRESH_TIME, TimeUnit.MINUTES)
            .build(new CacheLoader<String, AlarmRuleVo>() {
                @ParametersAreNonnullByDefault
                @Override
                public AlarmRuleVo load(String key) throws Exception {
                    /*  判读是否为空  */
                    Boolean empty = EMPTY_CACHE.getIfPresent(ALARM_RULE_EMPTY_KEY);
                    if (empty != null && empty) {
                        return null;
                    }
                    /*  已经初始化，且不包含当前Key  */
                    if (existAlarmRuleKeys != null && !existAlarmRuleKeys.contains(key)) {
                        return null;
                    }
                    logger.info("**********告警规则缓存加载**********");
                    /*  加载数据  */
                    Map<String, AlarmRuleVo> ruleMap = monitorService.fieldRules(fieldNum);
                    if (ruleMap == null || ruleMap.isEmpty()) {
                        EMPTY_CACHE.put(ALARM_RULE_EMPTY_KEY, true);
                        return null;
                    } else {
                        EMPTY_CACHE.invalidate(ALARM_RULE_EMPTY_KEY);
                        existAlarmRuleKeys = ruleMap.keySet();
                        ALARM_RULE.putAll(ruleMap);
                        return ruleMap.get(key);
                    }
                }
            });

    /**
     * 获取告警规则缓存
     *
     * @param key key
     * @return {@link AlarmRuleVo }
     */
    public AlarmRuleVo getAlarmRuleCache(String key) {
        try {
            return ALARM_RULE.get(key);
        } catch (Exception e) {
            return null;
        }
    }
}
