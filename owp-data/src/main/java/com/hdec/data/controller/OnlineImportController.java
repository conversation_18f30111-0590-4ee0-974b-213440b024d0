package com.hdec.data.controller;

import com.hdec.common.domain.R;
import com.hdec.data.service.OnlineImportService;
import com.hdec.data.vo.OnlineImportListVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 在线数据录入控制器
 *
 * <AUTHOR>
 */
@Api(tags = "在线数据录入")
@Validated
@RestController
@RequestMapping("api/data/onlineImport")
public class OnlineImportController {

    @Autowired
    private OnlineImportService importService;

    /**
     * 列表
     */
    @ApiOperation("列表")
    @PostMapping("list")
    public R list(@RequestBody OnlineImportListVo vo) {
        R r = importService.list(vo.getTime(), vo.getPointIds(), vo.getPointNos(), vo.getRate());
        return r;
    }

    /**
     * 保存
     */
    @ApiOperation("保存")
    @PostMapping("save/{rate}")
    public R save(@RequestHeader("fieldNum") String fieldNum, @RequestHeader(value = "sessionId") String sessionId,
                  @PathVariable("rate") String rate,
                  @RequestBody List<Map<String, String>> list) throws Exception {
        importService.save(sessionId, fieldNum, rate, list);
        return R.success("保存成功");
    }

}
