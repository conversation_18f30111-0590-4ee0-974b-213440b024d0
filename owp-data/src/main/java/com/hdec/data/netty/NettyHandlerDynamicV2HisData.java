package com.hdec.data.netty;

import cn.hutool.core.util.ByteUtil;
import com.hdec.common.domain.msg.Inst;
import com.hdec.data.service.KafkaService;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.nio.ByteOrder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * DM客户端消息处理类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class NettyHandlerDynamicV2HisData extends ChannelInboundHandlerAdapter {

    private static NettyHandlerDynamicV2HisData handler;

    @PostConstruct
    public void init() {
        handler = this;
    }

    @Autowired
    private KafkaService kafkaService;

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        log.info("有动态V2历史数据连接:{}",ctx.channel().remoteAddress());
        super.channelActive(ctx);
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        log.error("动态V2历史数据连接异常:{}", ctx.channel().remoteAddress(), cause);
        super.exceptionCaught(ctx, cause);
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        ByteBuf byteBuf = (ByteBuf) msg;
        byte[] bytes = new byte[byteBuf.readableBytes()];
        byteBuf.readBytes(bytes);
        byteBuf.release();
    }

    /**
     * 解析数据报文
     */
    private static List<Inst> parseDataMsg(byte[] bytes) {
//        0003
//        0001400CC92100033E869DA0000D3E4F203F0D0A0D0A
        List<Inst> instList = new ArrayList<>();
        int channelSize = bytesToUnsignedInt(getBytesByIndex(bytes, 18, 20));
        if (channelSize <= 0) {
            return Collections.emptyList();
        }

        Map<Integer, Float[]> dataMap = new HashMap<>();
        for (int i = 20; i < bytes.length - 8; i += 6) {
            int channelNo = bytesToUnsignedInt(getBytesByIndex(bytes, i, i + 2));
            float value = ByteUtil.bytesToFloat(getBytesByIndex(bytes, i + 2, i + 6), ByteOrder.BIG_ENDIAN);
            dataMap.put(channelNo, new Float[]{value});
        }

        Map<Integer, Map<Integer, Float[]>> groupedMap = dataMap.entrySet().stream()
                .collect(Collectors.groupingBy(
                        entry -> (entry.getKey() - 1) / 12,  // 分组依据
                        Collectors.toMap(
                                entry -> (entry.getKey() - 1) % 12,  // 将key重新映射到0~11
                                Map.Entry::getValue
                        )
                ));

        int deviceNo = ByteUtil.bytesToShort(getBytesByIndex(bytes, 4, 6), ByteOrder.BIG_ENDIAN);
        Date time = new Date(ByteUtil.bytesToLong(getBytesByIndex(bytes, 10, 18), ByteOrder.BIG_ENDIAN));
        groupedMap.forEach((machineNo, channelValues) -> {
            Inst inst = new Inst(deviceNo + "_" + machineNo, time, channelValues);
            instList.add(inst);
        });
        return instList;
    }

    public static int bytesToUnsignedInt(byte[] bytes) {
        // 大端格式：高字节在前
        return ((bytes[0] & 0xFF) << 8) | (bytes[1] & 0xFF);
    }

    /**
     * 按下标获取子字节数组
     */
    public static byte[] getBytesByIndex(byte[] bytes, int start, int end) {
        int len = end - start;
        byte[] target = new byte[len];
        System.arraycopy(bytes, start, target, 0, len);
        return target;
    }

    /*
    log.info("主机ID：{}", ByteUtil.bytesToShort(getBytesByIndex(bytes, 4, 6), ByteOrder.BIG_ENDIAN));
    log.info("数据帧ID：{}", ByteUtil.bytesToInt(getBytesByIndex(bytes, 6, 10), ByteOrder.BIG_ENDIAN));
    log.info("时间戳：{}", ByteUtil.bytesToLong(getBytesByIndex(bytes, 10, 18), ByteOrder.BIG_ENDIAN));
    log.info("时间：{}", TimeUtil.format2Second(new Date(ByteUtil.bytesToLong(getBytesByIndex(bytes, 10, 18), ByteOrder.BIG_ENDIAN))));
    log.info("通道数：{}", channelSize);


     */

}
